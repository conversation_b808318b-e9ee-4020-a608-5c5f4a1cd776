{"version": 3, "file": "useAnimation.js", "sourceRoot": "", "sources": ["../../src/useAnimation.ts"], "names": [], "mappings": ";;AAsCA,oCA+MC;AArPD,iCAAuD;AAEvD,mCAAoC;AAMpC,MAAM,aAAa,GAAG,CAAC,OAAuB,EAAsB,EAAE;IACpE,IAAI,OAAO,YAAY,WAAW;QAAE,OAAO,OAAO,CAAC;IACnD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,aAAa,GAAG,CAAC,OAAoB,EAAE,EAAE,CAAC;IAC9C,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,IAAI,EAAE,CAAC;CAC7D,CAAC;AACF,MAAM,YAAY,GAAG,CAAC,OAAoB,EAAE,EAAE,CAC5C,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,CAAC;AAChE,MAAM,cAAc,GAAG,CAAC,OAAoB,EAAE,EAAE,CAC9C,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC,CAAC;AAClE,MAAM,YAAY,GAAG,CAAC,OAAoB,EAAE,EAAE,CAC5C,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,CAAC;AAChE,MAAM,UAAU,GAAG,CAAC,OAAoB,EAAE,EAAE,CAC1C,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,CAAC;AAC9D,MAAM,eAAe,GAAG,CAAC,OAAoB,EAAE,EAAE,CAC/C,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,0BAA0B,CAAC,CAAC,CAAC;AAEnE;;;;;;;;;;GAUG;AACH,SAAgB,YAAY,CAC1B,SAAiD,EACjD,OAAgB,EAChB,EACE,UAAU,EACV,MAAM,EACN,OAAO,EACP,OAAO,EAMR;IAED,MAAM,yBAAyB,GAAG,IAAA,cAAM,EAAc,IAAI,CAAC,CAAC;IAC5D,MAAM,iBAAiB,GAAG,IAAA,cAAM,EAAC,MAAM,CAAC,CAAC;IACzC,MAAM,YAAY,GAAG,IAAA,cAAM,EAAC,KAAK,CAAC,CAAC;IAEnC,IAAA,uBAAe,EAAC,GAAG,EAAE;QACnB,8DAA8D;QAC9D,MAAM,cAAc,GAAG,iBAAiB,CAAC,OAAO,CAAC;QACjD,qDAAqD;QACrD,iBAAiB,CAAC,OAAO,GAAG,MAAM,CAAC;QAEnC,IACE,CAAC,OAAO;YACR,CAAC,SAAS,CAAC,OAAO;YAClB,mEAAmE;YACnE,CAAC,CAAC,SAAS,CAAC,OAAO,YAAY,WAAW,CAAC;YAC3C,4DAA4D;YAC5D,MAAM,CAAC,MAAM,KAAK,CAAC;YACnB,cAAc,CAAC,MAAM,KAAK,CAAC;YAC3B,MAAM,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM,EACvC,CAAC;YACD,OAAO;QACT,CAAC;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CACrC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EACd,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CACvB,CAAC;QAEF,MAAM,oBAAoB,GAAG,OAAO,CAAC,OAAO,CAC1C,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EACd,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CACvB,CAAC;QAEF,MAAM,qBAAqB,GAAG,oBAAoB;YAChD,CAAC,CAAC,UAAU,CAAC,iBAAS,CAAC,mBAAmB,CAAC;YAC3C,CAAC,CAAC,UAAU,CAAC,iBAAS,CAAC,oBAAoB,CAAC,CAAC;QAE/C,MAAM,mBAAmB,GAAG,oBAAoB;YAC9C,CAAC,CAAC,UAAU,CAAC,iBAAS,CAAC,iBAAiB,CAAC;YACzC,CAAC,CAAC,UAAU,CAAC,iBAAS,CAAC,kBAAkB,CAAC,CAAC;QAE7C,sEAAsE;QACtE,MAAM,sBAAsB,GAAG,yBAAyB,CAAC,OAAO,CAAC;QAEjE,0CAA0C;QAC1C,MAAM,cAAc,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,cAAc,YAAY,WAAW,EAAE,CAAC;YAC1C,yFAAyF;YACzF,gGAAgG;YAChG,MAAM,uBAAuB,GAAG,aAAa,CAAC,cAAc,CAAC,CAAC;YAC9D,uBAAuB,CAAC,OAAO,CAAC,CAAC,sBAAsB,EAAE,EAAE;gBACzD,IAAI,CAAC,CAAC,sBAAsB,YAAY,WAAW,CAAC;oBAAE,OAAO;gBAE7D,4DAA4D;gBAC5D,MAAM,uBAAuB,GAAG,YAAY,CAAC,sBAAsB,CAAC,CAAC;gBACrE,IACE,uBAAuB;oBACvB,sBAAsB,CAAC,QAAQ,CAAC,uBAAuB,CAAC,EACxD,CAAC;oBACD,sBAAsB,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC;gBAC9D,CAAC;gBAED,wDAAwD;gBACxD,MAAM,SAAS,GAAG,cAAc,CAAC,sBAAsB,CAAC,CAAC;gBACzD,IAAI,SAAS,EAAE,CAAC;oBACd,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;gBACpD,CAAC;gBAED,MAAM,OAAO,GAAG,YAAY,CAAC,sBAAsB,CAAC,CAAC;gBACrD,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;gBAChD,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,yBAAyB,CAAC,OAAO,GAAG,cAAc,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,yBAAyB,CAAC,OAAO,GAAG,IAAI,CAAC;QAC3C,CAAC;QAED,IACE,YAAY,CAAC,OAAO;YACpB,WAAW;YACX,yGAAyG;YACzG,OAAO,EACP,CAAC;YACD,OAAO;QACT,CAAC;QAED,MAAM,gBAAgB,GACpB,sBAAsB,YAAY,WAAW;YAC3C,CAAC,CAAC,aAAa,CAAC,sBAAsB,CAAC;YACvC,CAAC,CAAC,EAAE,CAAC;QAET,MAAM,eAAe,GAAG,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAEzD,IACE,eAAe;YACf,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,YAAY,WAAW,CAAC;YACxD,gBAAgB;YAChB,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,YAAY,WAAW,CAAC,EACzD,CAAC;YACD,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC;YAC5B,MAAM,gBAAgB,GAAmB,EAAE,CAAC;YAE5C,4EAA4E;YAC5E,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;YAC9C,yFAAyF;YACzF,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,KAAK,EAAE,CAAC;gBACV,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;YAC3B,CAAC;YAED,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,KAAK,EAAE,EAAE;gBAChD,MAAM,eAAe,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBAEhD,IAAI,CAAC,eAAe,EAAE,CAAC;oBACrB,OAAO;gBACT,CAAC;gBAED,8BAA8B;gBAC9B,cAAc,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;gBAC3C,cAAc,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBACzC,MAAM,SAAS,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;gBACjD,IAAI,SAAS,EAAE,CAAC;oBACd,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;gBACjD,CAAC;gBAED,MAAM,OAAO,GAAG,YAAY,CAAC,cAAc,CAAC,CAAC;gBAC7C,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;gBAC7C,CAAC;gBACD,kCAAkC;gBAElC,MAAM,OAAO,GAAG,GAAG,EAAE;oBACnB,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC;oBAE7B,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;wBACtB,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;oBACzC,CAAC;oBACD,IAAI,KAAK,EAAE,CAAC;wBACV,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;oBAC1B,CAAC;oBAED,IAAI,SAAS,EAAE,CAAC;wBACd,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;oBACpD,CAAC;oBACD,IAAI,OAAO,EAAE,CAAC;wBACZ,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;oBAChD,CAAC;oBACD,cAAc,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;oBACnC,cAAc,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;oBACnC,IAAI,cAAc,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;wBAC7C,cAAc,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;oBAC9C,CAAC;gBACH,CAAC,CAAC;gBACF,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAE/B,8BAA8B;gBAC9B,eAAe,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC;gBAC7C,eAAe,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;gBAC5C,eAAe,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBAC1C,eAAe,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;gBAEpD,oEAAoE;gBACpE,MAAM,kBAAkB,GAAG,eAAe,CAAC,eAAe,CAAC,CAAC;gBAC5D,IAAI,kBAAkB,EAAE,CAAC;oBACvB,kBAAkB,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;gBACzC,CAAC;gBAED,MAAM,iBAAiB,GAAG,cAAc,CAAC,eAAe,CAAC,CAAC;gBAC1D,IAAI,iBAAiB,EAAE,CAAC;oBACtB,iBAAiB,CAAC,SAAS,CAAC,GAAG,CAC7B,oBAAoB;wBAClB,CAAC,CAAC,UAAU,CAAC,iBAAS,CAAC,mBAAmB,CAAC;wBAC3C,CAAC,CAAC,UAAU,CAAC,iBAAS,CAAC,kBAAkB,CAAC,CAC7C,CAAC;oBACF,iBAAiB,CAAC,gBAAgB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;gBAC9D,CAAC;gBAED,MAAM,eAAe,GAAG,YAAY,CAAC,eAAe,CAAC,CAAC;gBACtD,IAAI,eAAe,EAAE,CAAC;oBACpB,eAAe,CAAC,SAAS,CAAC,GAAG,CAC3B,oBAAoB;wBAClB,CAAC,CAAC,UAAU,CAAC,iBAAS,CAAC,iBAAiB,CAAC;wBACzC,CAAC,CAAC,UAAU,CAAC,iBAAS,CAAC,gBAAgB,CAAC,CAC3C,CAAC;gBACJ,CAAC;gBAED,cAAc,CAAC,YAAY,CAAC,eAAe,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC;YAC1E,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC"}