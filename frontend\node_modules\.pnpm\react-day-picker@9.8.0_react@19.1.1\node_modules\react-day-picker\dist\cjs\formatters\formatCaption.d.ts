import { DateLib, type DateLibOptions } from "../classes/DateLib.js";
/**
 * Formats the caption of the month.
 *
 * @defaultValue `LLLL y` (e.g., "November 2022").
 * @param month The date representing the month.
 * @param options Configuration options for the date library.
 * @param dateLib The date library to use for formatting. If not provided, a new
 *   instance is created.
 * @returns The formatted caption as a string.
 * @group Formatters
 * @see https://daypicker.dev/docs/translation#custom-formatters
 */
export declare function formatCaption(month: Date, options?: DateLibOptions, dateLib?: DateLib): string;
/**
 * @private
 * @deprecated Use {@link formatCaption} instead.
 * @group Formatters
 */
export declare const formatMonthCaption: typeof formatCaption;
