{"version": 3, "file": "calculateFocusTarget.js", "sourceRoot": "", "sources": ["../../../src/helpers/calculateFocusTarget.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,UAAU,CAAC;AAInC,IAAK,mBAKJ;AALD,WAAK,mBAAmB;IACtB,+DAAS,CAAA;IACT,qEAAQ,CAAA;IACR,2EAAW,CAAA;IACX,mFAAe,CAAA;AACjB,CAAC,EALI,mBAAmB,KAAnB,mBAAmB,QAKvB;AAED;;;;;;;;GAQG;AACH,SAAS,cAAc,CAAC,SAAoB;IAC1C,OAAO,CACL,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC;QAC5B,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC;QAC1B,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAC5B,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,oBAAoB,CAClC,IAAmB,EACnB,YAA6C,EAC7C,UAAmC,EACnC,WAAoC;IAEpC,IAAI,WAAoC,CAAC;IAEzC,IAAI,wBAAwB,GAA6B,CAAC,CAAC,CAAC;IAC5D,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;QAEpC,IAAI,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9B,IACE,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC;gBAC1B,wBAAwB,GAAG,mBAAmB,CAAC,eAAe,EAC9D,CAAC;gBACD,WAAW,GAAG,GAAG,CAAC;gBAClB,wBAAwB,GAAG,mBAAmB,CAAC,eAAe,CAAC;YACjE,CAAC;iBAAM,IACL,WAAW,EAAE,SAAS,CAAC,GAAG,CAAC;gBAC3B,wBAAwB,GAAG,mBAAmB,CAAC,WAAW,EAC1D,CAAC;gBACD,WAAW,GAAG,GAAG,CAAC;gBAClB,wBAAwB,GAAG,mBAAmB,CAAC,WAAW,CAAC;YAC7D,CAAC;iBAAM,IACL,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC;gBACpB,wBAAwB,GAAG,mBAAmB,CAAC,QAAQ,EACvD,CAAC;gBACD,WAAW,GAAG,GAAG,CAAC;gBAClB,wBAAwB,GAAG,mBAAmB,CAAC,QAAQ,CAAC;YAC1D,CAAC;iBAAM,IACL,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC;gBACxB,wBAAwB,GAAG,mBAAmB,CAAC,KAAK,EACpD,CAAC;gBACD,WAAW,GAAG,GAAG,CAAC;gBAClB,wBAAwB,GAAG,mBAAmB,CAAC,KAAK,CAAC;YACvD,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,yCAAyC;QACzC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACtE,CAAC;IACD,OAAO,WAAW,CAAC;AACrB,CAAC"}