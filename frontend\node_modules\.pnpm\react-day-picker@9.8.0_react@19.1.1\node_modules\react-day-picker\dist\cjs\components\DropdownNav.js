"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DropdownNav = DropdownNav;
const react_1 = __importDefault(require("react"));
/**
 * Render the navigation dropdowns for the calendar.
 *
 * @group Components
 * @see https://daypicker.dev/guides/custom-components
 */
function DropdownNav(props) {
    return react_1.default.createElement("div", { ...props });
}
//# sourceMappingURL=DropdownNav.js.map