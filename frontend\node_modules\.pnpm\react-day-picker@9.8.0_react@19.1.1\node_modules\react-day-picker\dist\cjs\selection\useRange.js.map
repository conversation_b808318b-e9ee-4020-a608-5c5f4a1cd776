{"version": 3, "file": "useRange.js", "sourceRoot": "", "sources": ["../../../src/selection/useRange.tsx"], "names": [], "mappings": ";;AAsBA,4BA2DC;AA9ED,4EAAsE;AAOtE,gDAAuE;AACvE,wEAAkE;AAElE;;;;;;;;GAQG;AACH,SAAgB,QAAQ,CACtB,KAAQ,EACR,OAAgB;IAEhB,MAAM,EACJ,QAAQ,EACR,eAAe,EACf,QAAQ,EAAE,iBAAiB,EAC3B,QAAQ,EACR,QAAQ,EACT,GAAG,KAAmB,CAAC;IAExB,MAAM,CAAC,kBAAkB,EAAE,WAAW,CAAC,GAAG,IAAA,0CAAkB,EAC1D,iBAAiB,EACjB,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CACzC,CAAC;IAEF,MAAM,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB,CAAC;IAEpE,MAAM,UAAU,GAAG,CAAC,IAAU,EAAE,EAAE,CAChC,QAAQ,IAAI,IAAA,wCAAiB,EAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAEhE,MAAM,MAAM,GAAG,CACb,WAAiB,EACjB,SAAoB,EACpB,CAAyC,EACzC,EAAE;QACF,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,KAAmB,CAAC;QACzC,MAAM,QAAQ,GAAG,WAAW;YAC1B,CAAC,CAAC,IAAA,qBAAU,EAAC,WAAW,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC;YAChE,CAAC,CAAC,SAAS,CAAC;QAEd,IAAI,eAAe,IAAI,QAAQ,IAAI,QAAQ,EAAE,IAAI,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjE,IACE,IAAA,iCAAsB,EACpB,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,EACxC,QAAQ,EACR,OAAO,CACR,EACD,CAAC;gBACD,kDAAkD;gBAClD,QAAQ,CAAC,IAAI,GAAG,WAAW,CAAC;gBAC5B,QAAQ,CAAC,EAAE,GAAG,SAAS,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,WAAW,CAAC,QAAQ,CAAC,CAAC;QACxB,CAAC;QACD,QAAQ,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;QAEhD,OAAO,QAAQ,CAAC;IAClB,CAAC,CAAC;IAEF,OAAO;QACL,QAAQ;QACR,MAAM;QACN,UAAU;KACK,CAAC;AACpB,CAAC"}