"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Day = Day;
const react_1 = __importDefault(require("react"));
/**
 * Render a grid cell for a specific day in the calendar.
 *
 * Handles interaction and focus for the day. If you only need to change the
 * content of the day cell, consider swapping the `DayButton` component
 * instead.
 *
 * @group Components
 * @see https://daypicker.dev/guides/custom-components
 */
function Day(props) {
    const { day, modifiers, ...tdProps } = props;
    return react_1.default.createElement("td", { ...tdProps });
}
//# sourceMappingURL=Day.js.map