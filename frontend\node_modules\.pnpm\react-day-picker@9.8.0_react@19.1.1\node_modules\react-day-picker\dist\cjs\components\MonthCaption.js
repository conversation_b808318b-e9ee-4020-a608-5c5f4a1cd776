"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonthCaption = MonthCaption;
const react_1 = __importDefault(require("react"));
/**
 * Render the caption for a month in the calendar.
 *
 * @group Components
 * @see https://daypicker.dev/guides/custom-components
 */
function MonthCaption(props) {
    const { calendarMonth, displayIndex, ...divProps } = props;
    return react_1.default.createElement("div", { ...divProps });
}
//# sourceMappingURL=MonthCaption.js.map