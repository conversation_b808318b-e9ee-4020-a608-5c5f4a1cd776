#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/d/my_project/work/hermes/frontend/node_modules/.pnpm/jiti@2.5.1/node_modules/jiti/lib/node_modules:/proc/cygdrive/d/my_project/work/hermes/frontend/node_modules/.pnpm/jiti@2.5.1/node_modules/jiti/node_modules:/proc/cygdrive/d/my_project/work/hermes/frontend/node_modules/.pnpm/jiti@2.5.1/node_modules:/proc/cygdrive/d/my_project/work/hermes/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/d/my_project/work/hermes/frontend/node_modules/.pnpm/jiti@2.5.1/node_modules/jiti/lib/node_modules:/proc/cygdrive/d/my_project/work/hermes/frontend/node_modules/.pnpm/jiti@2.5.1/node_modules/jiti/node_modules:/proc/cygdrive/d/my_project/work/hermes/frontend/node_modules/.pnpm/jiti@2.5.1/node_modules:/proc/cygdrive/d/my_project/work/hermes/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../lib/jiti-cli.mjs" "$@"
else
  exec node  "$basedir/../../lib/jiti-cli.mjs" "$@"
fi
