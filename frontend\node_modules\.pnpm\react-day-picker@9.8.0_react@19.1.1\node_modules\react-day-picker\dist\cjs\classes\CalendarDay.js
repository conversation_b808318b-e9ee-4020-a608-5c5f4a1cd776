"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalendarDay = void 0;
const DateLib_js_1 = require("./DateLib.js");
/**
 * Represents a day displayed in the calendar.
 *
 * In DayPicker, a `CalendarDay` is a wrapper around a `Date` object that
 * provides additional information about the day, such as whether it belongs to
 * the displayed month.
 */
class CalendarDay {
    constructor(date, displayMonth, dateLib = DateLib_js_1.defaultDateLib) {
        this.date = date;
        this.displayMonth = displayMonth;
        this.outside = Boolean(displayMonth && !dateLib.isSameMonth(date, displayMonth));
        this.dateLib = dateLib;
    }
    /**
     * Checks if this day is equal to another `CalendarDay`, considering both the
     * date and the displayed month.
     *
     * @param day The `CalendarDay` to compare with.
     * @returns `true` if the days are equal, otherwise `false`.
     */
    isEqualTo(day) {
        return (this.dateLib.isSameDay(day.date, this.date) &&
            this.dateLib.isSameMonth(day.displayMonth, this.displayMonth));
    }
}
exports.CalendarDay = CalendarDay;
//# sourceMappingURL=CalendarDay.js.map