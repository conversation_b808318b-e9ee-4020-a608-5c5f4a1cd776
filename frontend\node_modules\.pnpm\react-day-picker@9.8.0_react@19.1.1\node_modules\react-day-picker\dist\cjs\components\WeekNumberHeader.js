"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeekNumberHeader = WeekNumberHeader;
const react_1 = __importDefault(require("react"));
/**
 * Render the header cell for the week numbers column.
 *
 * @group Components
 * @see https://daypicker.dev/guides/custom-components
 */
function WeekNumberHeader(props) {
    return react_1.default.createElement("th", { ...props });
}
//# sourceMappingURL=WeekNumberHeader.js.map