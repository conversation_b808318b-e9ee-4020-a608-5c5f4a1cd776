"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Weeks = Weeks;
const react_1 = __importDefault(require("react"));
/**
 * Render the container for the weeks in the month grid.
 *
 * @group Components
 * @see https://daypicker.dev/guides/custom-components
 */
function Weeks(props) {
    return react_1.default.createElement("tbody", { ...props });
}
//# sourceMappingURL=Weeks.js.map