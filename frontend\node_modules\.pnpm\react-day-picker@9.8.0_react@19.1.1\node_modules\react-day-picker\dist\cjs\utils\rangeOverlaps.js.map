{"version": 3, "file": "rangeOverlaps.js", "sourceRoot": "", "sources": ["../../../src/utils/rangeOverlaps.ts"], "names": [], "mappings": ";;AAcA,sCAWC;AAzBD,kDAAqD;AAErD,iEAA2D;AAE3D;;;;;;;;;GASG;AACH,SAAgB,aAAa,CAC3B,SAAmC,EACnC,UAAoC,EACpC,OAAO,GAAG,yBAAc;IAExB,OAAO,CACL,IAAA,wCAAiB,EAAC,SAAS,EAAE,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC;QAC7D,IAAA,wCAAiB,EAAC,SAAS,EAAE,UAAU,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC;QAC3D,IAAA,wCAAiB,EAAC,UAAU,EAAE,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC;QAC7D,IAAA,wCAAiB,EAAC,UAAU,EAAE,SAAS,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,CAC5D,CAAC;AACJ,CAAC"}